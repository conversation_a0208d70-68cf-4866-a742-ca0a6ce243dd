# WWWBuffer 数据传送功能 (保持原始ID)

## 概述
本功能实现了从 MSSQL 数据库 (wwwbuffer) 中的 Article 和 WaitingArticle 表到 MySQL 数据库中对应 portal 表的数据传送，**保持原始主键ID不变**。

## 核心特性

### 🔑 保持原始ID
- 传送过程中保持源数据库的主键ID不变
- 使用 `SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO"` 允许插入指定ID
- 临时禁用外键检查以确保数据完整性

### 📊 数据传送方法
在 `src/modules/base/service/common/portal.ts` 中的方法：

1. **transferCandidateArticleData()** - 传送候选文章数据
   - 保持原始ID: `id: parseInt(article.ID)`
   - 批量处理，每批 1000 条记录
   - 完整的错误处理和SQL设置恢复

2. **transferWaitingArticleData()** - 传送等待文章数据
   - 保持原始ID: `id: parseInt(waitingArticle.ID)`
   - 批量处理，每批 1000 条记录
   - 完整的错误处理和SQL设置恢复

3. **transferAllWwwbufferData()** - 传送所有数据
   - 依次调用上述两个方法
   - 返回传送统计信息

### 🔧 技术实现

#### SQL设置管理
```sql
-- 传送前设置
SET FOREIGN_KEY_CHECKS = 0;
SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";

-- 传送后恢复
SET FOREIGN_KEY_CHECKS = 1;
SET SQL_MODE = "";
```

#### ID映射策略
- **主键ID**: 使用原始数据库的ID (`id: parseInt(article.ID)`)
- **备份字段**: 同时保存到 `originalId` 字段作为备份

### 🌐 API 端点
在 `src/modules/base/controller/admin/common/portal.ts` 中：

- `POST /transferCandidateArticleData` - 传送候选文章数据(保持原始ID)
- `POST /transferWaitingArticleData` - 传送等待文章数据(保持原始ID)  
- `POST /transferAllWwwbufferData` - 传送所有wwwbuffer数据(保持原始ID)

## 字段映射

### Article -> CandidateArticle
| MSSQL 字段 | MySQL 字段 | 类型 | 说明 |
|-----------|-----------|------|------|
| **ID** | **id** | **bigint** | **主键ID(保持不变)** |
| ID | originalId | bigint | 备份原始ID |
| Url | url | varchar(500) | URL地址 |
| Title | title | varchar(500) | 标题 |
| Board | board | varchar(50) | 板块 |
| Author | author | varchar(50) | 作者 |
| Channel | channel | varchar(500) | 频道 |
| Tag | tag | varchar(50) | 标签 |
| Content | content | longtext | 内容 |
| RefContent | refContent | longtext | 引用内容 |
| AddedBy | addedBy | varchar(50) | 添加者 |
| ApprovedBy | approvedBy | varchar(50) | 审批者 |
| CreateTime | createTime | datetime | 创建时间 |
| ApprovedTime | approvedTime | datetime | 审批时间 |
| WWWArticleID | wwwArticleID | int | WWW文章ID |
| FixNews | fixNews | boolean | 固定新闻 |
| IsDeleted | isDeleted | boolean | 是否删除 |
| WAID | waid | bigint | WAID |

### WaitingArticle -> WaitingArticle
| MSSQL 字段 | MySQL 字段 | 类型 | 说明 |
|-----------|-----------|------|------|
| **ID** | **id** | **bigint** | **主键ID(保持不变)** |
| ID | originalId | bigint | 备份原始ID |
| ThreadID | threadID | bigint | 线程ID |
| Url | url | varchar(500) | URL地址 |
| Remark | remark | text | 备注 |
| Status | status | int | 状态 |
| CreateTime | createTime | datetime | 创建时间 |
| IsDeleted | isDeleted | boolean | 是否删除 |

## 使用方法

### 传送所有数据 (推荐)
```bash
curl -X POST http://localhost:8002/api/admin/common/portal/transferAllWwwbufferData
```

### 分别传送
```bash
# 传送候选文章数据
curl -X POST http://localhost:8002/api/admin/common/portal/transferCandidateArticleData

# 传送等待文章数据
curl -X POST http://localhost:8002/api/admin/common/portal/transferWaitingArticleData
```

## 返回格式

成功时返回：
```json
{
  "success": true,
  "candidateArticles": 1000,
  "waitingArticles": 500,
  "total": 1500
}
```

## 重要注意事项

### ⚠️ 数据安全
1. **备份数据**: 传送前请备份目标数据库
2. **测试环境**: 建议先在测试环境验证
3. **ID冲突**: 确保目标表中没有ID冲突的数据

### 🔒 事务安全
1. **外键检查**: 临时禁用外键检查，传送完成后恢复
2. **SQL模式**: 使用特殊SQL模式允许插入指定ID
3. **错误恢复**: 即使出错也会恢复SQL设置

### 📈 性能优化
1. **批量处理**: 每批处理1000条记录
2. **内存管理**: 及时清理批量数组
3. **日志记录**: 详细的进度日志

## 数据验证

传送完成后，可以通过以下SQL验证数据完整性：

```sql
-- 验证ID是否保持一致
SELECT COUNT(*) FROM portal_candidate_article WHERE id = originalId;
SELECT COUNT(*) FROM portal_waiting_article WHERE id = originalId;

-- 验证数据总数
SELECT COUNT(*) FROM portal_candidate_article;
SELECT COUNT(*) FROM portal_waiting_article;
```

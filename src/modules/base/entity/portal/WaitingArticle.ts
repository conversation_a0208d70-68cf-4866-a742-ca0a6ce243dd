import { Column, Entity } from 'typeorm';
import { BaseEntity } from '@cool-midway/core';

@Entity('portal_waiting_article', { synchronize: false })
export class PortalWaitingArticleEntity extends BaseEntity {
  @Column({ type: 'bigint' })
  threadID: string;

  @Column({ length: 500, default: '' })
  url: string;

  @Column({ type: 'text', nullable: true })
  remark: string | null;

  @Column({ type: 'int', default: 0 })
  status: number;

  @Column({ type: 'datetime', nullable: true })
  createTime: Date | null;

  @Column({ type: 'boolean', default: false })
  isDeleted: boolean;

  @Column({ type: 'bigint', nullable: true })
  originalId: string | null;
}

import { Provide, Inject, Post, Body, ALL, Get } from '@midwayjs/decorator';
import { Cool<PERSON>ontroller, BaseController } from '@cool-midway/core';
import { PortalArticleEntity } from '../../../entity/portal/Article';
import { PortalService } from '../../../service/common/portal';

import axios from 'axios';

/**
 * Portal
 */
@Provide()
@CoolController({
  api: ['update'],
  entity: PortalArticleEntity,
  service: PortalService,
})
export class CommonPortalController extends BaseController {
  @Inject()
  portalService: PortalService;

  @Post('/portalFindOne', { summary: 'Portal FindOne' })
  async portalFindOne(@Body(ALL) param: any) {
    return this.ok(await this.portalService.portalFindOne(param));
  }

  @Post('/portalCreate', { summary: '创建Portal' })
  async portalCreate(@Body(ALL) param: any) {    
    return this.ok(await this.portalService.portalCreate(param));
  }

  @Post('/portalUpdate', { summary: '编辑Portal' })
  async portalUpdate(@Body(ALL) param: any) {
    return this.ok(await this.portalService.portalUpdate(param));
  }

  @Post('/portalDelete', { summary: '删除Portal' })
  async portalDelete(@Body(ALL) param: any) {
    return this.ok(await this.portalService.portalDelete(param));
  }

  @Post('/portalPage', { summary: 'Portal列表' })
  async portalPage(@Body(ALL) param: any) {
    return this.ok(await this.portalService.portalPage(param));
  }

  @Post('/getAvailableTags', { summary: '获取可用标签' })
  async getAvailableTags(@Body(ALL) param: any) {
    return this.ok(await this.portalService.getAvailableTags(param));
  }

  @Get('/getPortalBannerIndex', { summary: '获取 Portal Banner 索引' })
  async getPortalBannerIndex() {
    return this.ok(await this.portalService.getPortalBannerIndex());
  }

  @Post('/setPortalBannerIndex', { summary: '设置 Portal Banner 索引' })
  async setPortalBannerIndex(@Body(ALL) param: any) {
    const { value } = param;
    if (value === undefined || value === null) {
      return this.fail('value 参数不能为空');
    }

    const numValue = parseInt(value.toString(), 10);
    if (isNaN(numValue)) {
      return this.fail('value 必须是有效的数字');
    }

    return this.ok(await this.portalService.setPortalBannerIndex(numValue));
  }

  @Post('/updateDisplayOrder', { summary: '更新文章显示顺序' })
  async updateDisplayOrder(@Body(ALL) param: any) {
    try {
      await this.portalService.updateDisplayOrder(param);
      return this.ok();
    } catch (error) {
      return this.fail(error.message);
    }
  }

  @Post('/tagSearch', { summary: '搜索标签' })
  async tagSearch(@Body(ALL) param: any) {
    try {
      const result = await axios.post('https://id.chasedream.com/api/v1/auth/tag/forum/search',
        param,
        {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
          timeout: 5000,
        }
      );

      return this.ok(result.data.data);
    } catch (error) {
      return this.fail(error.message);
    }
  }
}
